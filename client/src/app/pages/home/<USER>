<!-- Hero Section -->
<section
  class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-[#5521be] via-[#7125bb] to-[#c55ba1]">
  <!-- Animated background elements -->
  <div class="absolute inset-0">
    <!-- Floating Gears Animation -->
    <div class="absolute top-1/4 left-1/4 w-64 h-64 opacity-20">
      <ng-lottie [options]="gearsOptions" class="w-full h-full"></ng-lottie>
    </div>
    
    <!-- Analytics Dashboard Animation -->
    <div class="absolute bottom-1/4 right-1/4 w-96 h-96 opacity-15">
      <ng-lottie [options]="analyticsOptions" class="w-full h-full"></ng-lottie>
    </div>
    
    <!-- AI Robot Animation -->
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 opacity-25">
      <ng-lottie [options]="aiRobotOptions" class="w-full h-full"></ng-lottie>
    </div>
    
    <!-- Additional floating elements -->
    <div class="absolute top-3/4 left-1/6 w-24 h-24 opacity-20">
      <ng-lottie [options]="automationOptions" class="w-full h-full"></ng-lottie>
    </div>
    
    <div class="absolute top-1/6 right-1/6 w-40 h-40 opacity-10">
      <ng-lottie [options]="growthOptions" class="w-full h-full"></ng-lottie>
    </div>
  </div>

  <div class="relative z-10 text-center px-6 max-w-6xl mx-auto">
    <!-- Logo -->
    <div class="mb-8 animate-fade-in">
      <img alt="Chainmatic Logo" class="w-48 md:w-64 mx-auto mb-6 filter brightness-0 invert"
           src="/images/chainmatic.png"/>
      <p class="text-white/80 text-lg md:text-xl font-medium tracking-wider">
        AI Automation Solutions for B2B Growth
      </p>
    </div>

    <!-- Motto -->
    <div class="mb-8 animate-fade-in-delay">
      <p class="text-2xl md:text-3xl text-white/90 font-light italic">
        Chain The Chaos. Unleash The AI.
      </p>
    </div>

    <!-- Main Heading -->
    <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight animate-slide-up">
      <span class="block text-white">
        Grow your business,
      </span>
      <span class="block text-3xl md:text-5xl lg:text-6xl font-light text-white/90">
        <span class="relative">
          <span class="line-through text-white/50">not your payroll</span>
        </span>
      </span>
      <span class="block text-2xl md:text-4xl lg:text-5xl font-normal text-white mt-4">
        not your payroll
      </span>
    </h1>

    <!-- Subtitle -->
    <p class="text-lg md:text-xl text-white/80 mb-12 max-w-4xl mx-auto leading-relaxed animate-fade-in-delay">
      We build AI automation systems that scale 8-figure companies.
      <br class="hidden md:block">
      Let us automate your operations so you can focus on what matters most - growth.
    </p>

    <!-- CTA Buttons -->
    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center animate-slide-up-delay">
      <a class="group relative px-10 py-4 bg-white rounded-xl text-[#7125bb] font-bold text-lg hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-2xl"
         href="#services">
        <span class="relative z-10 flex items-center gap-3">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path clip-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                  fill-rule="evenodd"></path>
          </svg>
          See Our Solutions
        </span>
      </a>

      <a class="group px-10 py-4 border-2 border-white/30 rounded-xl text-white font-bold text-lg hover:bg-white/10 transition-all duration-300 transform hover:scale-105"
         routerLink="/admin/dashboard">
        <span class="flex items-center gap-2">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path clip-rule="evenodd"
                  d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  fill-rule="evenodd"></path>
          </svg>
          Client Portal
        </span>
      </a>
    </div>
  </div>

  <!-- Scroll indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
    <svg class="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path d="M19 14l-7 7m0 0l-7-7m7 7V3" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
    </svg>
  </div>
</section>

<!-- Services Section -->
<section class="py-24 bg-gradient-to-b from-white to-[#f5f0ff]" id="services">
  <div class="max-w-7xl mx-auto px-6">
    <!-- Section Header -->
    <div class="text-center mb-20">
      <h2 class="text-4xl md:text-5xl font-bold text-[#7125bb] mb-6">
        SERVICES
      </h2>
      <p class="text-2xl text-gray-600 max-w-3xl mx-auto font-light">
        Solutions that matter
      </p>
    </div>

    <!-- Services Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Service 1: Project Management Systems -->
      <div
        class="group bg-white rounded-2xl p-8 shadow-lg border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/10 transition-all duration-500 transform hover:-translate-y-3 animate-fade-in">
        <div
          class="w-16 h-16 bg-gradient-to-r from-[#7125bb] to-[#c55ba1] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500 animate-glow">
          <ng-lottie [options]="automationOptions" class="w-8 h-8"></ng-lottie>
        </div>
        <h3 class="text-2xl font-bold text-[#7125bb] mb-4 group-hover:gradient-text transition-all duration-300">Project
          Management Systems</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
          Bespoke project management systems used by 8-figure agencies & SaaS companies.
        </p>
      </div>

      <!-- Service 2: Custom CRM builds -->
      <div
        class="group bg-white rounded-2xl p-8 shadow-lg border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/10 transition-all duration-500 transform hover:-translate-y-3 animate-fade-in"
        style="animation-delay: 0.2s;">
        <div
          class="w-16 h-16 bg-gradient-to-r from-[#c55ba1] to-[#d734b1] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500 animate-glow"
          style="animation-delay: 0.5s;">
          <ng-lottie [options]="analyticsOptions" class="w-8 h-8"></ng-lottie>
        </div>
        <h3 class="text-2xl font-bold text-[#7125bb] mb-4 group-hover:gradient-text transition-all duration-300">Custom
          CRM builds</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
          Sales systems to track, iterate, and scale growth just like an 8-figure company.
        </p>
      </div>

      <!-- Service 3: Hiring systems -->
      <div
        class="group bg-white rounded-2xl p-8 shadow-lg border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/10 transition-all duration-500 transform hover:-translate-y-3 animate-fade-in"
        style="animation-delay: 0.4s;">
        <div
          class="w-16 h-16 bg-gradient-to-r from-[#d734b1] to-[#b46fc1] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500 animate-glow"
          style="animation-delay: 1s;">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path clip-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                  fill-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-[#7125bb] mb-4 group-hover:gradient-text transition-all duration-300">Hiring
          systems</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
          Automated processes that source, contact, and evaluate candidates for you.
        </p>
      </div>

      <!-- Service 4: Lead generation -->
      <div
        class="group bg-white rounded-2xl p-8 shadow-lg border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/10 transition-all duration-500 transform hover:-translate-y-3 animate-fade-in"
        style="animation-delay: 0.6s;">
        <div
          class="w-16 h-16 bg-gradient-to-r from-[#7125bb] to-[#d734b1] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500 animate-glow"
          style="animation-delay: 1.2s;">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
            <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-[#7125bb] mb-4 group-hover:gradient-text transition-all duration-300">Lead
          generation</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
          Scalable, affordable outbound & marketing systems to grow your company on autopilot.
        </p>
      </div>

      <!-- Service 5: Automated Service Fulfillment -->
      <div
        class="group bg-white rounded-2xl p-8 shadow-lg border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/10 transition-all duration-500 transform hover:-translate-y-3 animate-fade-in"
        style="animation-delay: 0.8s;">
        <div
          class="w-16 h-16 bg-gradient-to-r from-[#c55ba1] to-[#7125bb] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500 animate-glow"
          style="animation-delay: 1.4s;">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path clip-rule="evenodd"
                  d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                  fill-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-[#7125bb] mb-4 group-hover:gradient-text transition-all duration-300">
          Automated Service Fulfillment</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
          AI that automates key steps in your fulfillment process to reduce payroll.
        </p>
      </div>

      <!-- Service 6: SOPs & Consultation -->
      <div
        class="group bg-white rounded-2xl p-8 shadow-lg border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/10 transition-all duration-500 transform hover:-translate-y-3 animate-fade-in"
        style="animation-delay: 1s;">
        <div
          class="w-16 h-16 bg-gradient-to-r from-[#d734b1] to-[#c55ba1] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500 animate-glow"
          style="animation-delay: 1.6s;">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path clip-rule="evenodd"
                  d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z"
                  fill-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-[#7125bb] mb-4 group-hover:gradient-text transition-all duration-300">SOPs &
          Consultation</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
          We'll help clarify your offer, show you what 8-figure companies are doing, and build you a library of SOPs.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-24 bg-gradient-to-r from-[#f5f0ff] via-[#fff0f9] to-[#f5f0ff]">
  <div class="max-w-7xl mx-auto px-6">
    <!-- Section Header -->
    <div class="text-center mb-20">
      <h2 class="text-4xl md:text-5xl font-bold text-[#7125bb] mb-6">
        Why 8-Figure Companies Choose Us
      </h2>
      <p class="text-xl text-gray-600 max-w-4xl mx-auto">
        We don't just build tools - we architect systems that scale businesses from 6 to 8 figures
      </p>
    </div>

    <!-- Benefits -->
    <div class="grid md:grid-cols-3 gap-12 relative">
      <!-- Connecting lines for desktop -->
      <div
        class="hidden md:block absolute top-16 left-1/6 right-1/6 h-0.5 bg-gradient-to-r from-[#7125bb] via-[#c55ba1] to-[#d734b1]"></div>

      <!-- Benefit 1 -->
      <div class="text-center relative">
        <div
          class="w-32 h-32 bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
          <ng-lottie [options]="successOptions" class="w-16 h-16"></ng-lottie>
        </div>
        <h3 class="text-2xl font-bold text-[#7125bb] mb-4">Proven Systems</h3>
        <p class="text-gray-600 leading-relaxed">
          Battle-tested automation frameworks used by companies generating $10M+ annually. No experimental tech - only
          what works at scale.
        </p>
      </div>

      <!-- Benefit 2 -->
      <div class="text-center relative">
        <div
          class="w-32 h-32 bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
          <ng-lottie [options]="growthOptions" class="w-16 h-16"></ng-lottie>
        </div>
        <h3 class="text-2xl font-bold text-[#7125bb] mb-4">ROI Guaranteed</h3>
        <p class="text-gray-600 leading-relaxed">
          Our automations pay for themselves within 90 days through reduced payroll costs and increased efficiency.
          Typical clients save $50K+ annually.
        </p>
      </div>

      <!-- Benefit 3 -->
      <div class="text-center relative">
        <div
          class="w-32 h-32 bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
          <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path clip-rule="evenodd"
                  d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                  fill-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-[#7125bb] mb-4">Elite Partnership</h3>
        <p class="text-gray-600 leading-relaxed">
          We become your automation partner, not just a vendor. Ongoing optimization, strategic consulting, and growth
          planning included.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-24 bg-gradient-to-br from-[#7125bb] via-[#c55ba1] to-[#d734b1] relative overflow-hidden">
  <!-- Background decoration -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-full h-full opacity-20"
         style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%23ffffff&quot; fill-opacity=&quot;0.05&quot;%3E%3Ccircle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;4&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
  </div>

  <div class="max-w-4xl mx-auto text-center px-6 relative z-10">
    <h2 class="text-4xl md:text-6xl font-bold text-white mb-6">
      Ready to Scale to 8 Figures?
    </h2>
    <p class="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
      Stop drowning in operational chaos. Let AI handle the work while you focus on growth.
    </p>

    <!-- Key Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
      <div class="text-center">
        <div class="text-4xl font-bold text-white mb-2">$50K+</div>
        <div class="text-white/80">Annual Savings</div>
      </div>
      <div class="text-center">
        <div class="text-4xl font-bold text-white mb-2">90 Days</div>
        <div class="text-white/80">ROI Timeline</div>
      </div>
      <div class="text-center">
        <div class="text-4xl font-bold text-white mb-2">8-Figure</div>
        <div class="text-white/80">Proven Results</div>
      </div>
    </div>

    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
      <a
        class="group px-10 py-5 bg-white rounded-2xl text-[#7125bb] font-bold text-xl hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-2xl"
        href="mailto:<EMAIL>?subject=AI Automation Consultation&body=Hi, I'm interested in learning more about your AI automation solutions for my business.">
        <span class="flex items-center gap-3">
          <svg class="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" fill="currentColor"
               viewBox="0 0 20 20">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
          </svg>
          Book Free Consultation
        </span>
      </a>

      <a class="px-10 py-5 border-2 border-white/30 rounded-2xl text-white font-bold text-xl hover:bg-white/10 transition-all duration-300 transform hover:scale-105"
         routerLink="/admin/dashboard">
        <span class="flex items-center gap-2">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path clip-rule="evenodd"
                  d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  fill-rule="evenodd"></path>
          </svg>
          Client Portal
        </span>
      </a>
    </div>

    <p class="text-white/70 text-sm mt-8">
      🔒 No commitment required • Free 30-minute strategy session • Instant automation audit
    </p>
  </div>
</section>
