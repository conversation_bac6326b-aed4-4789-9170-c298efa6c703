/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(113, 37, 187, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(197, 91, 161, 0.5), 0 0 60px rgba(215, 52, 177, 0.3);
  }
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 1s ease-out;
}

.animate-fade-in-delay {
  animation: fadeIn 1s ease-out 0.3s both;
}

.animate-slide-up {
  animation: slideUp 1s ease-out;
}

.animate-slide-up-delay {
  animation: slideUp 1s ease-out 0.5s both;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #7125bb, #c55ba1, #d734b1);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(113, 37, 187, 0.2);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom gradient borders */
.gradient-border {
  position: relative;
  background: white;
  border-radius: 1rem;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(135deg, #7125bb, #c55ba1, #d734b1);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom button styles */
.btn-primary {
  background: linear-gradient(135deg, #7125bb, #c55ba1);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(113, 37, 187, 0.4);
}

/* Section padding for better spacing */
section {
  scroll-margin-top: 2rem;
}

/* Responsive typography */
@media (max-width: 768px) {
  .animate-slide-up,
  .animate-fade-in,
  .animate-fade-in-delay,
  .animate-slide-up-delay {
    animation-duration: 0.8s;
  }
}

/* Loading animation for images */
img {
  transition: opacity 0.3s ease;
}

img[src=""] {
  opacity: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #7125bb, #c55ba1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5521be, #b46fc1);
}

/* Z-index utilities for proper layering */
.z-0 {
  z-index: 0;
}

.z-1 {
  z-index: 1;
}

/* Ensure Lottie animations are properly layered */
ng-lottie {
  position: relative;
  z-index: inherit;
}

/* Fix for Lottie animations appearing behind other elements */
ng-lottie svg {
  position: relative;
  z-index: inherit;
}

/* Ensure service card icons are properly visible */
.group .w-16.h-16 {
  position: relative;
  z-index: 2;
}

.group .w-16.h-16 ng-lottie {
  position: relative;
  z-index: 3;
}

/* Ensure benefit section animations are visible */
.w-32.h-32 ng-lottie {
  position: relative;
  z-index: 2;
}

/* Force Lottie animations to be visible and interactive */
ng-lottie {
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure Lottie containers don't get clipped */
ng-lottie,
ng-lottie > div,
ng-lottie svg {
  overflow: visible !important;
}

/* Lottie animations styling */
ng-lottie {
  display: block;
  width: 100%;
  height: 100%;
}

/* Ensure Lottie SVG elements are properly sized */
ng-lottie svg {
  width: 100% !important;
  height: 100% !important;
}

/* Grid layout improvements for hero section */
.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-rows-12 {
  grid-template-rows: repeat(12, minmax(0, 1fr));
}

/* Ensure floating animations are visible and properly positioned */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Responsive adjustments for grid layout */
@media (max-width: 768px) {
  .grid-cols-12 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .col-span-8 {
    grid-column: span 6 / span 6;
  }

  .col-start-3 {
    grid-column-start: 1;
  }
}

/* Background animations positioning */
.opacity-5 {
  opacity: 0.05;
}

/* Ensure background animations don't interfere with content */
section[id="services"] .absolute {
  pointer-events: none;
}

section[id="services"] .relative {
  pointer-events: auto;
}

/* Enhanced animations for new design */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(113, 37, 187, 0.3), 0 0 40px rgba(197, 91, 161, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(113, 37, 187, 0.5), 0 0 60px rgba(197, 91, 161, 0.3);
  }
}

@keyframes rotate-glow {
  0% {
    transform: rotate(0deg);
    filter: hue-rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    filter: hue-rotate(360deg);
  }
}

/* Glass morphism enhancements */
.glass-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Enhanced gradient backgrounds */
.gradient-bg-primary {
  background: linear-gradient(135deg, #7125bb 0%, #c55ba1 50%, #d734b1 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, #f5f0ff 0%, #fff0f9 50%, #fef6fb 100%);
}

/* Enhanced button styles */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.7s ease;
}

.btn-enhanced:hover::before {
  left: 100%;
}

/* Enhanced card hover effects */
.card-enhanced {
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card-enhanced::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(113, 37, 187, 0.1), rgba(197, 91, 161, 0.1));
  opacity: 0;
  transition: opacity 0.5s ease;
}

.card-enhanced:hover::after {
  opacity: 1;
}

/* Floating elements animation */
.floating-element {
  animation: float 8s ease-in-out infinite;
}

.floating-element:nth-child(2) {
  animation-delay: -2s;
}

.floating-element:nth-child(3) {
  animation-delay: -4s;
}

.floating-element:nth-child(4) {
  animation-delay: -6s;
}

/* Enhanced responsive design */
@media (max-width: 1024px) {
  .text-7xl {
    font-size: 4rem;
  }

  .text-6xl {
    font-size: 3.5rem;
  }
}

@media (max-width: 768px) {
  .text-8xl {
    font-size: 3.5rem;
  }

  .text-7xl {
    font-size: 3rem;
  }

  .text-6xl {
    font-size: 2.5rem;
  }

  .py-32 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}