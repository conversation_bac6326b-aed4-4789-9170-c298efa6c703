<!-- Hero Section -->
<section class="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#f5f0ff] via-[#fff0f9] to-[#fef6fb]">
  <!-- Animated Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);"></div>
  </div>

  <!-- Floating Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute top-20 left-10 w-32 h-32 bg-[#7125bb]/10 rounded-full animate-float"></div>
    <div class="absolute top-40 right-20 w-24 h-24 bg-[#c55ba1]/10 rounded-full animate-float" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-[#d734b1]/10 rounded-full animate-float" style="animation-delay: 2s;"></div>
    <div class="absolute bottom-20 right-1/3 w-28 h-28 bg-[#b46fc1]/10 rounded-full animate-float" style="animation-delay: 0.5s;"></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 flex flex-col items-center justify-center min-h-screen px-6 text-center max-w-7xl mx-auto">

    <!-- Logo - Smaller, positioned as brand identifier -->
    <div class="mb-8 animate-fade-in">
      <img alt="Chainmatic Logo" class="w-32 md:w-40 mx-auto drop-shadow-lg opacity-90"
           src="/images/chainmatic.png"/>
    </div>

    <!-- Main Heading - Primary focus -->
    <div class="mb-8 space-y-4">
      <h1 class="text-6xl md:text-8xl lg:text-9xl font-bold leading-tight animate-slide-up">
        <span class="block bg-gradient-to-r from-[#7125bb] to-[#c55ba1] bg-clip-text text-transparent mb-2">
          Scale Your Business
        </span>
        <span class="block text-5xl md:text-7xl lg:text-8xl font-light text-[#7125bb]/80">
          Not Your Payroll
        </span>
      </h1>
    </div>

    <!-- Value Proposition - Clear and prominent -->
    <div class="mb-12">
      <p class="text-2xl md:text-4xl text-gray-700 max-w-5xl mx-auto leading-relaxed animate-fade-in-delay font-light">
        We architect AI automation systems that power 8-figure companies.
      </p>
      <p class="text-xl md:text-2xl text-gray-500 max-w-4xl mx-auto leading-relaxed mt-4">
        Let intelligent automation handle operations while you focus on exponential growth.
      </p>
    </div>

    <!-- Brand Tagline - Supporting element -->
    <div class="mb-12">
      <div class="bg-white/90 backdrop-blur-sm border border-[#e5d6f0] rounded-2xl px-8 py-4 inline-block shadow-lg">
        <p class="text-lg md:text-xl text-[#d734b1] font-medium italic">
          "Chain The Chaos. Unleash The AI."
        </p>
      </div>
    </div>

    <!-- Company Description - Context -->
    <div class="mb-16">
      <div class="bg-white/80 backdrop-blur-sm border border-[#e5d6f0] rounded-xl px-6 py-3 inline-block shadow-sm">
        <p class="text-[#7125bb] text-base md:text-lg font-medium tracking-wide">
          AI Automation Solutions for B2B Growth
        </p>
      </div>
    </div>

    <!-- CTA Buttons -->
    <div class="flex flex-col sm:flex-row gap-8 justify-center items-center animate-slide-up-delay mb-16">
      <a class="group relative px-12 py-5 bg-gradient-to-r from-[#7125bb] to-[#c55ba1] rounded-2xl text-white font-bold text-xl hover:from-[#c55ba1] hover:to-[#d734b1] transition-all duration-500 transform hover:scale-105 shadow-2xl overflow-hidden"
         href="#services">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
        <span class="relative z-10 flex items-center gap-3">
          <svg class="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Explore Our Solutions
        </span>
      </a>

      <a class="group px-12 py-5 border-2 border-[#7125bb] rounded-2xl text-[#7125bb] font-bold text-xl hover:bg-[#7125bb] hover:text-white transition-all duration-500 transform hover:scale-105 bg-white/90 backdrop-blur-sm"
         routerLink="/admin/dashboard">
        <span class="flex items-center gap-3">
          <svg class="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path clip-rule="evenodd"
                  d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  fill-rule="evenodd"></path>
          </svg>
          Client Portal
        </span>
      </a>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2">
      <div class="animate-bounce">
        <div class="w-8 h-12 border-2 border-[#7125bb]/40 rounded-full flex justify-center">
          <div class="w-1 h-3 bg-[#7125bb]/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Services Section -->
<section class="py-32 bg-gradient-to-b from-[#f5f0ff] via-white to-[#fff0f9] relative overflow-hidden" id="services">
  <!-- Background Decoration -->
  <div class="absolute inset-0 opacity-5">
    <div class="absolute top-0 left-0 w-full h-full" style="background-image: radial-gradient(circle at 20% 20%, #7125bb 0%, transparent 50%), radial-gradient(circle at 80% 80%, #d734b1 0%, transparent 50%);"></div>
  </div>

  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-24">
      <div class="inline-block bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-4">
        <h2 class="text-5xl md:text-6xl font-bold">
          Our Solutions
        </h2>
      </div>
      <p class="text-2xl md:text-3xl text-gray-600 max-w-4xl mx-auto font-light leading-relaxed">
        Enterprise-grade automation systems that scale 8-figure businesses
      </p>
      <div class="w-24 h-1 bg-gradient-to-r from-[#7125bb] to-[#d734b1] mx-auto mt-8 rounded-full"></div>
    </div>

    <!-- Services Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
      <!-- Service 1: Project Management Systems -->
      <div class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden">
        <!-- Background Gradient on Hover -->
        <div class="absolute inset-0 bg-gradient-to-br from-[#f5f0ff] to-[#fff0f9] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div class="w-20 h-20 bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <ng-lottie [options]="automationOptions" class="w-10 h-10"></ng-lottie>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Project Management Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Bespoke project management systems used by 8-figure agencies & SaaS companies.
          </p>

          <!-- Hover Effect Border -->
          <div class="absolute inset-0 border-2 border-transparent group-hover:border-[#7125bb]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>

      <!-- Service 2: Custom CRM builds -->
      <div class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#c55ba1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden" style="animation-delay: 0.2s;">
        <div class="absolute inset-0 bg-gradient-to-br from-[#fff0f9] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div class="w-20 h-20 bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <ng-lottie [options]="analyticsOptions" class="w-10 h-10"></ng-lottie>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Custom CRM Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Sales systems to track, iterate, and scale growth just like an 8-figure company.
          </p>

          <div class="absolute inset-0 border-2 border-transparent group-hover:border-[#c55ba1]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>

      <!-- Service 3: Hiring systems -->
      <div class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#d734b1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden" style="animation-delay: 0.4s;">
        <div class="absolute inset-0 bg-gradient-to-br from-[#fef6fb] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div class="w-20 h-20 bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" fill-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Intelligent Hiring Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Automated processes that source, contact, and evaluate candidates for you.
          </p>

          <div class="absolute inset-0 border-2 border-transparent group-hover:border-[#d734b1]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>

      <!-- Service 4: Lead generation -->
      <div class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden" style="animation-delay: 0.6s;">
        <div class="absolute inset-0 bg-gradient-to-br from-[#f5f0ff] to-[#fef6fb] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div class="w-20 h-20 bg-gradient-to-br from-[#7125bb] to-[#d734b1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
              <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Lead Generation Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Scalable, affordable outbound & marketing systems to grow your company on autopilot.
          </p>

          <div class="absolute inset-0 border-2 border-transparent group-hover:border-[#7125bb]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>

      <!-- Service 5: Automated Service Fulfillment -->
      <div class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#c55ba1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden" style="animation-delay: 0.8s;">
        <div class="absolute inset-0 bg-gradient-to-br from-[#fff0f9] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div class="w-20 h-20 bg-gradient-to-br from-[#c55ba1] to-[#7125bb] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" fill-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Service Fulfillment AI
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            AI that automates key steps in your fulfillment process to reduce payroll.
          </p>

          <div class="absolute inset-0 border-2 border-transparent group-hover:border-[#c55ba1]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>

      <!-- Service 6: SOPs & Consultation -->
      <div class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#d734b1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden" style="animation-delay: 1s;">
        <div class="absolute inset-0 bg-gradient-to-br from-[#fef6fb] to-[#fff0f9] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div class="w-20 h-20 bg-gradient-to-br from-[#d734b1] to-[#c55ba1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd" d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z" fill-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Strategic Consultation
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            We'll help clarify your offer, show you what 8-figure companies are doing, and build you a library of SOPs.
          </p>

          <div class="absolute inset-0 border-2 border-transparent group-hover:border-[#d734b1]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-32 bg-gradient-to-br from-[#fff0f9] via-[#f5f0ff] to-[#fef6fb] relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute top-0 left-0 w-96 h-96 bg-[#7125bb] rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-[#d734b1] rounded-full blur-3xl"></div>
  </div>

  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-24">
      <h2 class="text-5xl md:text-6xl font-bold mb-8 leading-tight">
        <span class="bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent">Why 8-Figure Companies</span>
        <span class="block text-[#7125bb]">Choose Chainmatic</span>
      </h2>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
        We don't just build tools - we architect intelligent systems that scale businesses from 6 to 8 figures
      </p>
      <div class="w-32 h-1 bg-gradient-to-r from-[#7125bb] to-[#d734b1] mx-auto mt-8 rounded-full"></div>
    </div>

    <!-- Benefits -->
    <div class="grid md:grid-cols-3 gap-16 relative">
      <!-- Benefit 1 -->
      <div class="text-center relative group">
        <div class="relative">
          <div class="w-40 h-40 bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl group-hover:scale-110 transition-all duration-500 border border-white/30">
            <ng-lottie [options]="successOptions" class="w-20 h-20"></ng-lottie>
          </div>
          <!-- Glow effect -->
          <div class="absolute inset-0 w-40 h-40 bg-[#7125bb]/20 rounded-3xl mx-auto blur-xl group-hover:blur-2xl transition-all duration-500"></div>
        </div>
        <h3 class="text-3xl font-bold text-[#7125bb] mb-6 group-hover:text-[#c55ba1] transition-colors duration-300">Proven Systems</h3>
        <p class="text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300">
          Battle-tested automation frameworks used by companies generating $10M+ annually. No experimental tech - only what works at scale.
        </p>
      </div>

      <!-- Benefit 2 -->
      <div class="text-center relative group">
        <div class="relative">
          <div class="w-40 h-40 bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl group-hover:scale-110 transition-all duration-500 border border-white/30">
            <ng-lottie [options]="growthOptions" class="w-20 h-20"></ng-lottie>
          </div>
          <div class="absolute inset-0 w-40 h-40 bg-[#c55ba1]/20 rounded-3xl mx-auto blur-xl group-hover:blur-2xl transition-all duration-500"></div>
        </div>
        <h3 class="text-3xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">ROI Guaranteed</h3>
        <p class="text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300">
          Our automations pay for themselves within 90 days through reduced payroll costs and increased efficiency. Typical clients save $50K+ annually.
        </p>
      </div>

      <!-- Benefit 3 -->
      <div class="text-center relative group">
        <div class="relative">
          <div class="w-40 h-40 bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl group-hover:scale-110 transition-all duration-500 border border-white/30">
            <ng-lottie [options]="gearsOptions" class="w-20 h-20"></ng-lottie>
          </div>
          <div class="absolute inset-0 w-40 h-40 bg-[#d734b1]/20 rounded-3xl mx-auto blur-xl group-hover:blur-2xl transition-all duration-500"></div>
        </div>
        <h3 class="text-3xl font-bold text-[#7125bb] mb-6 group-hover:text-[#b46fc1] transition-colors duration-300">Elite Partnership</h3>
        <p class="text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300">
          We become your automation partner, not just a vendor. Ongoing optimization, strategic consulting, and growth planning included.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-32 bg-gradient-to-b from-[#f5f0ff] via-white to-[#fff0f9] relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0 opacity-5">
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-[#7125bb] to-[#d734b1] rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-[#c55ba1] to-[#b46fc1] rounded-full blur-3xl"></div>
  </div>

  <div class="max-w-6xl mx-auto text-center px-6 relative z-10">
    <!-- Main CTA -->
    <div class="bg-white/80 backdrop-blur-sm border border-[#e5d6f0] rounded-3xl p-12 md:p-16 shadow-2xl">
      <h2 class="text-5xl md:text-7xl font-bold bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-8 leading-tight">
        Ready to Scale to 8 Figures?
      </h2>
      <p class="text-2xl md:text-3xl text-gray-600 mb-12 leading-relaxed font-light">
        Stop drowning in operational chaos. Let AI handle the work while you focus on exponential growth.
      </p>

      <!-- Key Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-12 mb-16">
        <div class="text-center group">
          <div class="bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-2xl p-8 group-hover:scale-105 transition-all duration-500 shadow-xl">
            <div class="text-5xl font-bold text-white mb-3">$50K+</div>
            <div class="text-white/90 text-lg font-medium">Annual Savings</div>
          </div>
        </div>
        <div class="text-center group">
          <div class="bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-2xl p-8 group-hover:scale-105 transition-all duration-500 shadow-xl">
            <div class="text-5xl font-bold text-white mb-3">90 Days</div>
            <div class="text-white/90 text-lg font-medium">ROI Timeline</div>
          </div>
        </div>
        <div class="text-center group">
          <div class="bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-2xl p-8 group-hover:scale-105 transition-all duration-500 shadow-xl">
            <div class="text-5xl font-bold text-white mb-3">8-Figure</div>
            <div class="text-white/90 text-lg font-medium">Proven Results</div>
          </div>
        </div>
      </div>

      <!-- CTA Buttons -->
      <div class="flex flex-col sm:flex-row gap-8 justify-center items-center mb-12">
        <a class="group relative px-16 py-6 bg-gradient-to-r from-[#7125bb] to-[#c55ba1] rounded-2xl text-white font-bold text-2xl hover:from-[#c55ba1] hover:to-[#d734b1] transition-all duration-500 transform hover:scale-105 shadow-2xl overflow-hidden"
           href="mailto:<EMAIL>?subject=AI Automation Consultation&body=Hi, I'm interested in learning more about your AI automation solutions for my business.">
          <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
          <span class="relative z-10 flex items-center gap-4">
            <svg class="w-8 h-8 group-hover:rotate-12 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
            </svg>
            Book Free Consultation
          </span>
        </a>

        <a class="group px-16 py-6 border-3 border-[#7125bb] rounded-2xl text-[#7125bb] font-bold text-2xl hover:bg-[#7125bb] hover:text-white transition-all duration-500 transform hover:scale-105 bg-white shadow-lg"
           routerLink="/admin/dashboard">
          <span class="flex items-center gap-4">
            <svg class="w-8 h-8 group-hover:translate-x-2 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" fill-rule="evenodd"></path>
            </svg>
            Client Portal
          </span>
        </a>
      </div>

      <!-- Trust Indicators -->
      <div class="bg-[#f5f0ff]/50 border border-[#e5d6f0] rounded-2xl p-6 inline-block">
        <p class="text-[#7125bb] text-lg font-medium">
          🔒 No commitment required • Free 30-minute strategy session • Instant automation audit
        </p>
      </div>
    </div>
  </div>
</section>
