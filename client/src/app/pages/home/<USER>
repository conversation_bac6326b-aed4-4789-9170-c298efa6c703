/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(113, 37, 187, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(197, 91, 161, 0.5), 0 0 60px rgba(215, 52, 177, 0.3);
  }
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 1s ease-out;
}

.animate-fade-in-delay {
  animation: fadeIn 1s ease-out 0.3s both;
}

.animate-slide-up {
  animation: slideUp 1s ease-out;
}

.animate-slide-up-delay {
  animation: slideUp 1s ease-out 0.5s both;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #7125bb, #c55ba1, #d734b1);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(113, 37, 187, 0.2);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom gradient borders */
.gradient-border {
  position: relative;
  background: white;
  border-radius: 1rem;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(135deg, #7125bb, #c55ba1, #d734b1);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom button styles */
.btn-primary {
  background: linear-gradient(135deg, #7125bb, #c55ba1);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(113, 37, 187, 0.4);
}

/* Section padding for better spacing */
section {
  scroll-margin-top: 2rem;
}

/* Responsive typography */
@media (max-width: 768px) {
  .animate-slide-up,
  .animate-fade-in,
  .animate-fade-in-delay,
  .animate-slide-up-delay {
    animation-duration: 0.8s;
  }
}

/* Loading animation for images */
img {
  transition: opacity 0.3s ease;
}

img[src=""] {
  opacity: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #7125bb, #c55ba1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5521be, #b46fc1);
}

/* Z-index utilities for proper layering */
.z-0 {
  z-index: 0;
}

.z-1 {
  z-index: 1;
}

/* Ensure Lottie animations are properly layered */
ng-lottie {
  position: relative;
  z-index: inherit;
}

/* Fix for Lottie animations appearing behind other elements */
ng-lottie svg {
  position: relative;
  z-index: inherit;
}

/* Ensure service card icons are properly visible */
.group .w-16.h-16 {
  position: relative;
  z-index: 2;
}

.group .w-16.h-16 ng-lottie {
  position: relative;
  z-index: 3;
}

/* Ensure benefit section animations are visible */
.w-32.h-32 ng-lottie {
  position: relative;
  z-index: 2;
}

/* Force Lottie animations to be visible and interactive */
ng-lottie {
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure Lottie containers don't get clipped */
ng-lottie,
ng-lottie > div,
ng-lottie svg {
  overflow: visible !important;
}

/* Lottie animations styling */
ng-lottie {
  display: block;
  width: 100%;
  height: 100%;
}

/* Ensure Lottie SVG elements are properly sized */
ng-lottie svg {
  width: 100% !important;
  height: 100% !important;
}

/* Grid layout improvements for hero section */
.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-rows-12 {
  grid-template-rows: repeat(12, minmax(0, 1fr));
}

/* Ensure floating animations are visible and properly positioned */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Responsive adjustments for grid layout */
@media (max-width: 768px) {
  .grid-cols-12 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .col-span-8 {
    grid-column: span 6 / span 6;
  }

  .col-start-3 {
    grid-column-start: 1;
  }
}

/* Background animations positioning */
.opacity-5 {
  opacity: 0.05;
}

/* Ensure background animations don't interfere with content */
section[id="services"] .absolute {
  pointer-events: none;
}

section[id="services"] .relative {
  pointer-events: auto;
}

/* Enhanced animations for new design */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(113, 37, 187, 0.3), 0 0 40px rgba(197, 91, 161, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(113, 37, 187, 0.5), 0 0 60px rgba(197, 91, 161, 0.3);
  }
}

@keyframes rotate-glow {
  0% {
    transform: rotate(0deg);
    filter: hue-rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    filter: hue-rotate(360deg);
  }
}

/* Glass morphism enhancements */
.glass-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Enhanced gradient backgrounds - Soft pastels */
.gradient-bg-primary {
  background: linear-gradient(135deg, #f5f0ff 0%, #fff0f9 50%, #fef6fb 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, #fff0f9 0%, #f5f0ff 50%, #fef6fb 100%);
}

.gradient-bg-accent {
  background: linear-gradient(135deg, #fef6fb 0%, #f5f0ff 50%, #fff0f9 100%);
}

/* Enhanced button styles */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.7s ease;
}

.btn-enhanced:hover::before {
  left: 100%;
}

/* Enhanced card hover effects */
.card-enhanced {
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card-enhanced::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(113, 37, 187, 0.1), rgba(197, 91, 161, 0.1));
  opacity: 0;
  transition: opacity 0.5s ease;
}

.card-enhanced:hover::after {
  opacity: 1;
}

/* Floating elements animation */
.floating-element {
  animation: float 8s ease-in-out infinite;
}

.floating-element:nth-child(2) {
  animation-delay: -2s;
}

.floating-element:nth-child(3) {
  animation-delay: -4s;
}

.floating-element:nth-child(4) {
  animation-delay: -6s;
}

/* Enhanced responsive design */
@media (max-width: 1024px) {
  .text-7xl {
    font-size: 4rem;
  }

  .text-6xl {
    font-size: 3.5rem;
  }
}

@media (max-width: 768px) {
  .text-8xl {
    font-size: 3.5rem;
  }

  .text-7xl {
    font-size: 3rem;
  }

  .text-6xl {
    font-size: 2.5rem;
  }

  .py-32 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

/* Soft pastel theme utilities */
.text-soft-primary {
  color: #7125bb;
}

.text-soft-secondary {
  color: #c55ba1;
}

.text-soft-accent {
  color: #d734b1;
}

.bg-soft-primary {
  background-color: #f5f0ff;
}

.bg-soft-secondary {
  background-color: #fff0f9;
}

.bg-soft-accent {
  background-color: #fef6fb;
}

/* Enhanced border colors for soft theme */
.border-soft-primary {
  border-color: #e5d6f0;
}

.border-soft-secondary {
  border-color: #f0e6f7;
}

/* Soft shadows */
.shadow-soft {
  box-shadow: 0 4px 20px rgba(113, 37, 187, 0.1);
}

.shadow-soft-lg {
  box-shadow: 0 8px 30px rgba(113, 37, 187, 0.15);
}

/* Hero section typography hierarchy */
.hero-heading {
  font-weight: 800;
  letter-spacing: -0.02em;
  line-height: 0.9;
}

.hero-subheading {
  font-weight: 300;
  letter-spacing: -0.01em;
  line-height: 1.1;
}

.hero-value-prop {
  font-weight: 300;
  letter-spacing: -0.005em;
  line-height: 1.4;
}

/* Enhanced button hierarchy */
.btn-primary-hero {
  font-size: 1.5rem;
  font-weight: 700;
  padding: 1.5rem 4rem;
  box-shadow: 0 10px 40px rgba(113, 37, 187, 0.3);
}

.btn-secondary-hero {
  font-size: 1.125rem;
  font-weight: 600;
  padding: 1rem 2rem;
}

.btn-tertiary-hero {
  font-size: 1.125rem;
  font-weight: 600;
  padding: 1rem 2rem;
}

/* Trust indicators styling */
.trust-indicator {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(229, 214, 240, 0.8);
}

/* Improved spacing for hero content */
.hero-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Enhanced logo positioning */
.hero-logo {
  opacity: 0.9;
  filter: drop-shadow(0 4px 20px rgba(113, 37, 187, 0.1));
}

/* Logo reveal animation as content element - Large size */
.logo-reveal-element {
  filter: drop-shadow(0 8px 40px rgba(113, 37, 187, 0.2));
  transition: all 0.7s ease;
}

.logo-reveal-element:hover {
  transform: scale(1.03);
  filter: drop-shadow(0 12px 60px rgba(113, 37, 187, 0.35));
}

/* Logo reveal container styling - Large size */
.logo-reveal-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 100vw;
  overflow: visible;
}

.logo-reveal-container::before {
  content: '';
  position: absolute;
  inset: -20px;
  background: radial-gradient(circle, rgba(113, 37, 187, 0.08) 0%, rgba(197, 91, 161, 0.04) 50%, transparent 80%);
  border-radius: 40px;
  opacity: 0;
  transition: opacity 0.7s ease;
}

.logo-reveal-container:hover::before {
  opacity: 1;
}

/* Ensure Lottie animation is properly sized - Large version */
.logo-reveal-container ng-lottie {
  display: block;
  width: 100%;
  height: 100%;
}

.logo-reveal-container ng-lottie svg {
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
}

/* Responsive adjustments for large logo reveal */
@media (max-width: 640px) {
  .logo-reveal-container {
    max-width: 95vw;
  }

  /* Smaller hover effect on mobile */
  .logo-reveal-element:hover {
    transform: scale(1.02);
  }
}

@media (max-width: 480px) {
  .logo-reveal-container {
    max-width: 90vw;
  }
}

/* Tools section styling */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 2rem;
  align-items: center;
  justify-items: center;
}

.tool-card {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tool-card:hover {
  background: #f3f4f6;
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 10px 25px rgba(113, 37, 187, 0.15);
}

.tool-icon {
  width: 3rem;
  height: 3rem;
  transition: transform 0.3s ease;
}

.tool-card:hover .tool-icon {
  transform: scale(1.1);
}

/* Responsive adjustments for tools grid */
@media (max-width: 768px) {
  .tools-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .tool-card {
    padding: 1rem;
  }

  .tool-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
}

@media (max-width: 480px) {
  .tools-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .tool-card {
    padding: 0.75rem;
  }

  .tool-icon {
    width: 2rem;
    height: 2rem;
  }
}

/* Marquee animation for tools */
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  animation: marquee 30s linear infinite;
  width: calc(200% + 3rem); /* Double width for seamless loop */
}

/* Pause animation on hover */
.animate-marquee:hover {
  animation-play-state: paused;
}

/* Tools marquee container */
.tools-marquee-container {
  overflow: hidden;
  white-space: nowrap;
  position: relative;
}

.tools-marquee-container::before,
.tools-marquee-container::after {
  content: '';
  position: absolute;
  top: 0;
  width: 100px;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.tools-marquee-container::before {
  left: 0;
  background: linear-gradient(to right, white, transparent);
}

.tools-marquee-container::after {
  right: 0;
  background: linear-gradient(to left, white, transparent);
}

/* Better visual hierarchy for mobile */
@media (max-width: 768px) {
  .hero-heading {
    font-size: 3.5rem;
    line-height: 0.95;
  }

  .hero-subheading {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .hero-value-prop {
    font-size: 1.25rem;
    line-height: 1.5;
  }

  .btn-primary-hero {
    font-size: 1.25rem;
    padding: 1.25rem 3rem;
  }
}