import { Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import { LottieComponent, AnimationOptions } from 'ngx-lottie';

@Component({
  selector: 'chm-home',
  imports: [RouterLink, LottieComponent],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent {
  // AI Robot Animation for Hero
  aiRobotOptions: AnimationOptions = {
    path: '/animations/ai-robot.json',
    autoplay: true,
    loop: true
  };

  // Floating Gears Animation
  gearsOptions: AnimationOptions = {
    path: '/animations/gears.json',
    autoplay: true,
    loop: true
  };

  // Data Analytics Animation
  analyticsOptions: AnimationOptions = {
    path: '/animations/analytics.json',
    autoplay: true,
    loop: true
  };

  // Growth Chart Animation
  growthOptions: AnimationOptions = {
    path: '/animations/growth-chart.json',
    autoplay: true,
    loop: true
  };

  // Automation Process Animation
  automationOptions: AnimationOptions = {
    path: '/animations/automation.json',
    autoplay: true,
    loop: true
  };

  // Success Checkmark Animation
  successOptions: AnimationOptions = {
    path: '/animations/success.json',
    autoplay: true,
    loop: false
  };

  // Logo Reveal Animation
  logoRevealOptions: AnimationOptions = {
    path: '/animations/logo-reveal.json',
    autoplay: true,
    loop: true
  };
}
