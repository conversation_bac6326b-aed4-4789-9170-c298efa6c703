<!-- Hero Section -->
<section class="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#f5f0ff] via-[#fff0f9] to-[#fef6fb]">
  <!-- Animated Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0"
         style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);"></div>
  </div>

  <!-- Background Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-32 h-32 bg-[#7125bb]/10 rounded-full animate-float"></div>
    <div class="absolute top-40 right-20 w-24 h-24 bg-[#c55ba1]/10 rounded-full animate-float"
         style="animation-delay: 1s;"></div>
    <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-[#d734b1]/10 rounded-full animate-float"
         style="animation-delay: 2s;"></div>
    <div class="absolute bottom-20 right-1/3 w-28 h-28 bg-[#b46fc1]/10 rounded-full animate-float"
         style="animation-delay: 0.5s;"></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 flex flex-col items-center justify-center min-h-screen px-6 text-center hero-content">

    <!-- Logo - Prominent brand identifier -->
    <div class="mb-24 animate-fade-in">
      <img alt="Chainmatic Logo" class="w-3/4 md:w-2/3 mx-auto hero-logo"
           src="/images/chainmatic.png"/>
    </div>

    <!-- Main Heading - Primary focus -->
    <div class="mb-10 space-y-3">
      <h1 class="text-3xl md:text-5xl lg:text-6xl font-semibold animate-slide-up">
        <span class="block bg-gradient-to-r from-[#7125bb] to-[#c55ba1] bg-clip-text text-transparent mb-2">
          Scale up, staff down.
        </span>
        <span class="block text-2xl md:text-4xl lg:text-5xl hero-subheading text-[#7125bb]/80">
          AI Automation Solutions for B2B Growth
        </span>
      </h1>
    </div>

    <!-- Logo Reveal Animation - Visual Element -->
    <div class="mb-16 flex justify-center animate-fade-in-delay">
      <div class="w-[500px] md:w-[700px] lg:w-[900px] h-48 md:h-64 lg:h-80 logo-reveal-container">
        <ng-lottie [options]="logoRevealOptions" class="logo-reveal-element"></ng-lottie>
      </div>
    </div>


    <!-- Primary CTA - Most important action -->
    <div class="mb-24 animate-slide-up-delay">
      <a
        class="group relative inline-flex items-center gap-4 btn-primary-hero bg-gradient-to-r from-[#7125bb] to-[#c55ba1] rounded-2xl text-white hover:from-[#c55ba1] hover:to-[#d734b1] transition-all duration-500 transform hover:scale-105 overflow-hidden"
        href="mailto:<EMAIL>?subject=AI Automation Consultation&body=Hi, I'm interested in learning more about your AI automation solutions for my business.">
        <div
          class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
        <svg class="w-8 h-8 group-hover:rotate-12 transition-transform duration-300 relative z-10" fill="currentColor"
             viewBox="0 0 20 20">
          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
        </svg>
        <span class="relative z-10">Book Free Consultation</span>
      </a>
    </div>


    <!-- Trust Indicators -->
    <div class="mb-12">
      <div class="trust-indicator rounded-2xl px-8 py-6 inline-block shadow-lg">
        <div class="flex flex-col sm:flex-row items-center gap-6 text-sm text-gray-600">
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    fill-rule="evenodd"></path>
            </svg>
            <span class="font-medium">No commitment required</span>
          </div>
          <div class="hidden sm:block w-px h-6 bg-gray-300"></div>
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                    fill-rule="evenodd"></path>
            </svg>
            <span class="font-medium">Free 30-minute strategy session</span>
          </div>
          <div class="hidden sm:block w-px h-6 bg-gray-300"></div>
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    fill-rule="evenodd"></path>
            </svg>
            <span class="font-medium">Instant automation audit</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Services Section -->
<section class="py-32 bg-gradient-to-b from-[#f5f0ff] via-white to-[#fff0f9] relative overflow-hidden" id="services">
  <!-- Background Decoration -->
  <div class="absolute inset-0 opacity-5">
    <div class="absolute top-0 left-0 w-full h-full"
         style="background-image: radial-gradient(circle at 20% 20%, #7125bb 0%, transparent 50%), radial-gradient(circle at 80% 80%, #d734b1 0%, transparent 50%);"></div>
  </div>

  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-24">
      <div class="inline-block bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-4">
        <h2 class="text-5xl md:text-6xl font-bold">
          Our Solutions
        </h2>
      </div>
      <p class="text-2xl md:text-3xl text-gray-600 max-w-4xl mx-auto font-light leading-relaxed">
        Enterprise-grade automation systems that scale 8-figure businesses
      </p>
      <div class="w-24 h-1 bg-gradient-to-r from-[#7125bb] to-[#d734b1] mx-auto mt-8 rounded-full"></div>
    </div>

    <!-- Services Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
      <!-- Service 1: Project Management Systems -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden">
        <!-- Background Gradient on Hover -->
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#f5f0ff] to-[#fff0f9] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <ng-lottie [options]="automationOptions" class="w-10 h-10"></ng-lottie>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Project Management Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Bespoke project management systems used by 8-figure agencies & SaaS companies.
          </p>


        </div>
      </div>

      <!-- Service 2: Custom CRM builds -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#c55ba1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 0.2s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fff0f9] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <ng-lottie [options]="analyticsOptions" class="w-10 h-10"></ng-lottie>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Custom CRM Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Sales systems to track, iterate, and scale growth just like an 8-figure company.
          </p>


        </div>
      </div>

      <!-- Service 3: Hiring systems -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#d734b1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 0.4s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fef6fb] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                    fill-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Intelligent Hiring Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Automated processes that source, contact, and evaluate candidates for you.
          </p>


        </div>
      </div>

      <!-- Service 4: Lead generation -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 0.6s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#f5f0ff] to-[#fef6fb] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#7125bb] to-[#d734b1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
              <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Lead Generation Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Scalable, affordable outbound & marketing systems to grow your company on autopilot.
          </p>


        </div>
      </div>

      <!-- Service 5: Automated Service Fulfillment -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#c55ba1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 0.8s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fff0f9] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#c55ba1] to-[#7125bb] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                    fill-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Service Fulfillment AI
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            AI that automates key steps in your fulfillment process to reduce payroll.
          </p>


        </div>
      </div>

      <!-- Service 6: SOPs & Consultation -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#d734b1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 1s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fef6fb] to-[#fff0f9] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#d734b1] to-[#c55ba1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z"
                    fill-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Strategic Consultation
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            We'll help clarify your offer, show you what 8-figure companies are doing, and build you a library of SOPs.
          </p>


        </div>
      </div>
    </div>
  </div>
</section>

<!-- Tools Section -->
<section class="py-24 bg-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <h2
        class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-6">
        We Use The Tools That You Love
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
        We integrate seamlessly with your existing tech stack and favorite platforms
      </p>
    </div>

    <!-- Tools Marquee -->
    <div class="tools-marquee-container py-8">
      <div class="flex animate-marquee space-x-12 items-center">
        <!-- First set of logos -->
        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Slack" class="max-w-full max-h-full object-contain" src="/images/tools/slack.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Notion" class="max-w-full max-h-full object-contain" src="/images/tools/notion.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="ClickUp" class="max-w-full max-h-full object-contain" src="/images/tools/clickup.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Monday.com" class="max-w-full max-h-full object-contain" src="/images/tools/monday.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Trello" class="max-w-full max-h-full object-contain" src="/images/tools/trello.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="HubSpot" class="max-w-full max-h-full object-contain" src="/images/tools/hubspot.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="n8n" class="max-w-full max-h-full object-contain" src="/images/tools/n8n.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="ChatGPT" class="max-w-full max-h-full object-contain" src="/images/tools/chatgpt.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Google Gemini" class="max-w-full max-h-full object-contain" src="/images/tools/gemini.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Claude" class="max-w-full max-h-full object-contain" src="/images/tools/claude.png">
        </div>

        <!-- Duplicate set for seamless loop -->
        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Slack" class="max-w-full max-h-full object-contain" src="/images/tools/slack.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Notion" class="max-w-full max-h-full object-contain" src="/images/tools/notion.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="ClickUp" class="max-w-full max-h-full object-contain" src="/images/tools/clickup.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Monday.com" class="max-w-full max-h-full object-contain" src="/images/tools/monday.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Trello" class="max-w-full max-h-full object-contain" src="/images/tools/trello.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="HubSpot" class="max-w-full max-h-full object-contain" src="/images/tools/hubspot.svg">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="n8n" class="max-w-full max-h-full object-contain" src="/images/tools/n8n.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="ChatGPT" class="max-w-full max-h-full object-contain" src="/images/tools/chatgpt.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Google Gemini" class="max-w-full max-h-full object-contain" src="/images/tools/gemini.png">
        </div>

        <div
          class="flex-shrink-0 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-300 p-3">
          <img alt="Claude" class="max-w-full max-h-full object-contain" src="/images/tools/claude.png">
        </div>
      </div>
    </div>


    <!-- Additional Text -->
    <div class="text-center mt-16">
      <p class="text-lg text-gray-500 max-w-2xl mx-auto">
        And many more tools in your existing tech stack. We adapt to your workflow, not the other way around.
      </p>
    </div>
  </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-32 bg-gradient-to-br from-[#fff0f9] via-[#f5f0ff] to-[#fef6fb] relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute top-0 left-0 w-96 h-96 bg-[#7125bb] rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-[#d734b1] rounded-full blur-3xl"></div>
  </div>

  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-24">
      <h2 class="text-5xl md:text-6xl font-bold mb-8 leading-tight">
        <span
          class="bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent">Why 8-Figure Companies</span>
        <span class="block text-[#7125bb]">Choose Chainmatic</span>
      </h2>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
        We don't just build tools - we architect intelligent systems that scale businesses from 6 to 8 figures
      </p>
      <div class="w-32 h-1 bg-gradient-to-r from-[#7125bb] to-[#d734b1] mx-auto mt-8 rounded-full"></div>
    </div>

    <!-- Benefits -->
    <div class="grid md:grid-cols-3 gap-16 relative">
      <!-- Benefit 1 -->
      <div class="text-center relative group">
        <div class="relative">
          <div
            class="w-40 h-40 bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl group-hover:scale-110 transition-all duration-500 border border-white/30">
            <ng-lottie [options]="successOptions" class="w-20 h-20"></ng-lottie>
          </div>
          <!-- Glow effect -->
          <div
            class="absolute inset-0 w-40 h-40 bg-[#7125bb]/20 rounded-3xl mx-auto blur-xl group-hover:blur-2xl transition-all duration-500"></div>
        </div>
        <h3 class="text-3xl font-bold text-[#7125bb] mb-6 group-hover:text-[#c55ba1] transition-colors duration-300">
          Proven Systems</h3>
        <p class="text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300">
          Battle-tested automation frameworks used by companies generating $10M+ annually. No experimental tech - only
          what works at scale.
        </p>
      </div>

      <!-- Benefit 2 -->
      <div class="text-center relative group">
        <div class="relative">
          <div
            class="w-40 h-40 bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl group-hover:scale-110 transition-all duration-500 border border-white/30">
            <ng-lottie [options]="growthOptions" class="w-20 h-20"></ng-lottie>
          </div>
          <div
            class="absolute inset-0 w-40 h-40 bg-[#c55ba1]/20 rounded-3xl mx-auto blur-xl group-hover:blur-2xl transition-all duration-500"></div>
        </div>
        <h3 class="text-3xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">ROI
          Guaranteed</h3>
        <p class="text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300">
          Our automations pay for themselves within 90 days through reduced payroll costs and increased efficiency.
          Typical clients save $50K+ annually.
        </p>
      </div>

      <!-- Benefit 3 -->
      <div class="text-center relative group">
        <div class="relative">
          <div
            class="w-40 h-40 bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl group-hover:scale-110 transition-all duration-500 border border-white/30">
            <ng-lottie [options]="gearsOptions" class="w-20 h-20"></ng-lottie>
          </div>
          <div
            class="absolute inset-0 w-40 h-40 bg-[#d734b1]/20 rounded-3xl mx-auto blur-xl group-hover:blur-2xl transition-all duration-500"></div>
        </div>
        <h3 class="text-3xl font-bold text-[#7125bb] mb-6 group-hover:text-[#b46fc1] transition-colors duration-300">
          Elite Partnership</h3>
        <p class="text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300">
          We become your automation partner, not just a vendor. Ongoing optimization, strategic consulting, and growth
          planning included.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-32 bg-gradient-to-b from-[#f5f0ff] via-white to-[#fff0f9] relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0 opacity-5">
    <div
      class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-[#7125bb] to-[#d734b1] rounded-full blur-3xl"></div>
    <div
      class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-[#c55ba1] to-[#b46fc1] rounded-full blur-3xl"></div>
  </div>

  <div class="max-w-6xl mx-auto text-center px-6 relative z-10">
    <!-- Main CTA -->
    <div class="bg-white/80 backdrop-blur-sm border border-[#e5d6f0] rounded-3xl p-12 md:p-16 shadow-2xl">
      <h2
        class="text-5xl md:text-7xl font-bold bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-8 leading-tight">
        Ready to Scale to 8 Figures?
      </h2>
      <p class="text-2xl md:text-3xl text-gray-600 mb-12 leading-relaxed font-light">
        Stop drowning in operational chaos. Let AI handle the work while you focus on exponential growth.
      </p>

      <!-- Key Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-12 mb-16">
        <div class="text-center group">
          <div
            class="bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-2xl p-8 group-hover:scale-105 transition-all duration-500 shadow-xl">
            <div class="text-5xl font-bold text-white mb-3">$50K+</div>
            <div class="text-white/90 text-lg font-medium">Annual Savings</div>
          </div>
        </div>
        <div class="text-center group">
          <div
            class="bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-2xl p-8 group-hover:scale-105 transition-all duration-500 shadow-xl">
            <div class="text-5xl font-bold text-white mb-3">90 Days</div>
            <div class="text-white/90 text-lg font-medium">ROI Timeline</div>
          </div>
        </div>
        <div class="text-center group">
          <div
            class="bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-2xl p-8 group-hover:scale-105 transition-all duration-500 shadow-xl">
            <div class="text-5xl font-bold text-white mb-3">8-Figure</div>
            <div class="text-white/90 text-lg font-medium">Proven Results</div>
          </div>
        </div>
      </div>

      <!-- CTA Buttons -->
      <div class="flex flex-col sm:flex-row gap-8 justify-center items-center mb-12">
        <a
          class="group relative px-16 py-6 bg-gradient-to-r from-[#7125bb] to-[#c55ba1] rounded-2xl text-white font-bold text-2xl hover:from-[#c55ba1] hover:to-[#d734b1] transition-all duration-500 transform hover:scale-105 shadow-2xl overflow-hidden"
          href="mailto:<EMAIL>?subject=AI Automation Consultation&body=Hi, I'm interested in learning more about your AI automation solutions for my business.">
          <div
            class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
          <span class="relative z-10 flex items-center gap-4">
            <svg class="w-8 h-8 group-hover:rotate-12 transition-transform duration-300" fill="currentColor"
                 viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
            </svg>
            Book Free Consultation
          </span>
        </a>

        <a
          class="group px-16 py-6 border-3 border-[#7125bb] rounded-2xl text-[#7125bb] font-bold text-2xl hover:bg-[#7125bb] hover:text-white transition-all duration-500 transform hover:scale-105 bg-white shadow-lg"
          routerLink="/admin/dashboard">
          <span class="flex items-center gap-4">
            <svg class="w-8 h-8 group-hover:translate-x-2 transition-transform duration-300" fill="currentColor"
                 viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    fill-rule="evenodd"></path>
            </svg>
            Client Portal
          </span>
        </a>
      </div>

      <!-- Trust Indicators -->
      <div class="bg-[#f5f0ff]/50 border border-[#e5d6f0] rounded-2xl p-6 inline-block">
        <p class="text-[#7125bb] text-lg font-medium">
          🔒 No commitment required • Free 30-minute strategy session • Instant automation audit
        </p>
      </div>
    </div>
  </div>
</section>
