<!-- Hero Section -->
<section class="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#f5f0ff] via-[#fff0f9] to-[#fef6fb]">
  <!-- Animated Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0"
         style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);"></div>
  </div>

  <!-- Background Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-32 h-32 bg-[#7125bb]/10 rounded-full animate-float"></div>
    <div class="absolute top-40 right-20 w-24 h-24 bg-[#c55ba1]/10 rounded-full animate-float"
         style="animation-delay: 1s;"></div>
    <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-[#d734b1]/10 rounded-full animate-float"
         style="animation-delay: 2s;"></div>
    <div class="absolute bottom-20 right-1/3 w-28 h-28 bg-[#b46fc1]/10 rounded-full animate-float"
         style="animation-delay: 0.5s;"></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 flex flex-col items-center justify-center min-h-screen px-6 text-center hero-content">

    <!-- Logo - Prominent brand identifier -->
    <div class="mb-8 animate-fade-in">
      <img alt="Chainmatic Logo" class="w-72 md:w-96 mx-auto hero-logo"
           src="/images/chainmatic.png"/>
    </div>

    <!-- Main Heading - Primary focus -->
    <div class="mb-10 space-y-3">
      <h1 class="text-4xl md:text-6xl lg:text-7xl hero-heading animate-slide-up">
        <span class="block bg-gradient-to-r from-[#7125bb] to-[#c55ba1] bg-clip-text text-transparent mb-2">
          Scale up, staff down.
        </span>
        <span class="block text-3xl md:text-5xl lg:text-6xl hero-subheading text-[#7125bb]/80">
          Scale up, staff down.
        </span>
      </h1>
    </div>

    <!-- Value Proposition - Clear and prominent -->
    <div class="mb-10">
      <p class="text-xl md:text-2xl lg:text-3xl text-gray-700 max-w-4xl mx-auto hero-value-prop animate-fade-in-delay">
        We architect AI automation systems that power 8-figure companies.
      </p>
      <p class="text-lg md:text-xl text-gray-500 max-w-3xl mx-auto leading-relaxed mt-4">
        Let intelligent automation handle operations while you focus on exponential growth.
      </p>
    </div>

    <!-- Brand Tagline - Supporting element -->
    <div class="mb-8">
      <div class="bg-white/90 backdrop-blur-sm border border-[#e5d6f0] rounded-2xl px-8 py-4 inline-block shadow-lg">
        <p class="text-lg md:text-xl text-[#d734b1] font-medium italic">
          "Chain The Chaos. Unleash The AI."
        </p>
      </div>
    </div>

    <!-- Logo Reveal Animation - Visual Element -->
    <div class="mb-16 flex justify-center animate-fade-in-delay">
      <div class="w-[500px] md:w-[700px] lg:w-[900px] h-48 md:h-64 lg:h-80 logo-reveal-container">
        <ng-lottie [options]="logoRevealOptions" class="logo-reveal-element"></ng-lottie>
      </div>
    </div>

    <!-- Company Description - Context -->
    <div class="mb-16">
      <div class="bg-white/80 backdrop-blur-sm border border-[#e5d6f0] rounded-xl px-6 py-3 inline-block shadow-sm">
        <p class="text-[#7125bb] text-base md:text-lg font-medium tracking-wide">
          AI Automation Solutions for B2B Growth
        </p>
      </div>
    </div>

    <!-- Primary CTA - Most important action -->
    <div class="mb-8 animate-slide-up-delay">
      <a
        class="group relative inline-flex items-center gap-4 btn-primary-hero bg-gradient-to-r from-[#7125bb] to-[#c55ba1] rounded-2xl text-white hover:from-[#c55ba1] hover:to-[#d734b1] transition-all duration-500 transform hover:scale-105 overflow-hidden"
        href="mailto:<EMAIL>?subject=AI Automation Consultation&body=Hi, I'm interested in learning more about your AI automation solutions for my business.">
        <div
          class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
        <svg class="w-8 h-8 group-hover:rotate-12 transition-transform duration-300 relative z-10" fill="currentColor"
             viewBox="0 0 20 20">
          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
        </svg>
        <span class="relative z-10">Book Free Consultation</span>
      </a>
    </div>

    <!-- Secondary Actions -->
    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
      <a
        class="group inline-flex items-center gap-3 btn-secondary-hero border-2 border-[#7125bb] rounded-xl text-[#7125bb] hover:bg-[#7125bb] hover:text-white transition-all duration-300 transform hover:scale-105 bg-white shadow-lg"
        href="#services">
        <svg class="w-6 h-6 group-hover:translate-y-1 transition-transform duration-300" fill="currentColor"
             viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>Explore Solutions</span>
      </a>

      <a
        class="group inline-flex items-center gap-3 btn-tertiary-hero text-[#7125bb] hover:text-[#c55ba1] transition-all duration-300"
        routerLink="/admin/dashboard">
        <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="currentColor"
             viewBox="0 0 20 20">
          <path clip-rule="evenodd"
                d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                fill-rule="evenodd"></path>
        </svg>
        <span>Client Portal</span>
      </a>
    </div>

    <!-- Trust Indicators -->
    <div class="mb-12">
      <div class="trust-indicator rounded-2xl px-8 py-6 inline-block shadow-lg">
        <div class="flex flex-col sm:flex-row items-center gap-6 text-sm text-gray-600">
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    fill-rule="evenodd"></path>
            </svg>
            <span class="font-medium">No commitment required</span>
          </div>
          <div class="hidden sm:block w-px h-6 bg-gray-300"></div>
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                    fill-rule="evenodd"></path>
            </svg>
            <span class="font-medium">Free 30-minute strategy session</span>
          </div>
          <div class="hidden sm:block w-px h-6 bg-gray-300"></div>
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    fill-rule="evenodd"></path>
            </svg>
            <span class="font-medium">Instant automation audit</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2">
      <div class="animate-bounce">
        <div class="w-8 h-12 border-2 border-[#7125bb]/40 rounded-full flex justify-center">
          <div class="w-1 h-3 bg-[#7125bb]/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Services Section -->
<section class="py-32 bg-gradient-to-b from-[#f5f0ff] via-white to-[#fff0f9] relative overflow-hidden" id="services">
  <!-- Background Decoration -->
  <div class="absolute inset-0 opacity-5">
    <div class="absolute top-0 left-0 w-full h-full"
         style="background-image: radial-gradient(circle at 20% 20%, #7125bb 0%, transparent 50%), radial-gradient(circle at 80% 80%, #d734b1 0%, transparent 50%);"></div>
  </div>

  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-24">
      <div class="inline-block bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-4">
        <h2 class="text-5xl md:text-6xl font-bold">
          Our Solutions
        </h2>
      </div>
      <p class="text-2xl md:text-3xl text-gray-600 max-w-4xl mx-auto font-light leading-relaxed">
        Enterprise-grade automation systems that scale 8-figure businesses
      </p>
      <div class="w-24 h-1 bg-gradient-to-r from-[#7125bb] to-[#d734b1] mx-auto mt-8 rounded-full"></div>
    </div>

    <!-- Services Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
      <!-- Service 1: Project Management Systems -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden">
        <!-- Background Gradient on Hover -->
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#f5f0ff] to-[#fff0f9] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <ng-lottie [options]="automationOptions" class="w-10 h-10"></ng-lottie>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Project Management Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Bespoke project management systems used by 8-figure agencies & SaaS companies.
          </p>

          <!-- Hover Effect Border -->
          <div
            class="absolute inset-0 border-2 border-transparent group-hover:border-[#7125bb]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>

      <!-- Service 2: Custom CRM builds -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#c55ba1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 0.2s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fff0f9] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <ng-lottie [options]="analyticsOptions" class="w-10 h-10"></ng-lottie>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Custom CRM Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Sales systems to track, iterate, and scale growth just like an 8-figure company.
          </p>

          <div
            class="absolute inset-0 border-2 border-transparent group-hover:border-[#c55ba1]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>

      <!-- Service 3: Hiring systems -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#d734b1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 0.4s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fef6fb] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                    fill-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Intelligent Hiring Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Automated processes that source, contact, and evaluate candidates for you.
          </p>

          <div
            class="absolute inset-0 border-2 border-transparent group-hover:border-[#d734b1]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>

      <!-- Service 4: Lead generation -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#7125bb]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 0.6s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#f5f0ff] to-[#fef6fb] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#7125bb] to-[#d734b1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
              <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Lead Generation Systems
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            Scalable, affordable outbound & marketing systems to grow your company on autopilot.
          </p>

          <div
            class="absolute inset-0 border-2 border-transparent group-hover:border-[#7125bb]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>

      <!-- Service 5: Automated Service Fulfillment -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#c55ba1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 0.8s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fff0f9] to-[#f5f0ff] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#c55ba1] to-[#7125bb] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                    fill-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Service Fulfillment AI
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            AI that automates key steps in your fulfillment process to reduce payroll.
          </p>

          <div
            class="absolute inset-0 border-2 border-transparent group-hover:border-[#c55ba1]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>

      <!-- Service 6: SOPs & Consultation -->
      <div
        class="group relative bg-white rounded-3xl p-8 shadow-xl border border-[#e5d6f0] hover:shadow-2xl hover:shadow-[#d734b1]/20 transition-all duration-700 transform hover:-translate-y-4 animate-fade-in overflow-hidden"
        style="animation-delay: 1s;">
        <div
          class="absolute inset-0 bg-gradient-to-br from-[#fef6fb] to-[#fff0f9] opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

        <div class="relative z-10">
          <div
            class="w-20 h-20 bg-gradient-to-br from-[#d734b1] to-[#c55ba1] rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z"
                    fill-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">
            Strategic Consultation
          </h3>
          <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-lg">
            We'll help clarify your offer, show you what 8-figure companies are doing, and build you a library of SOPs.
          </p>

          <div
            class="absolute inset-0 border-2 border-transparent group-hover:border-[#d734b1]/20 rounded-3xl transition-all duration-500"></div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Tools Section -->
<section class="py-24 bg-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <h2
        class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-6">
        We Use The Tools That You Love
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
        We integrate seamlessly with your existing tech stack and favorite platforms
      </p>
    </div>

    <!-- Tools Marquee -->
    <div class="relative overflow-hidden">
      <div class="flex animate-marquee space-x-16 items-center">
      <!-- Row 1 -->
      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <img alt="Slack" class="w-12 h-12 group-hover:scale-110 transition-transform duration-300"
             src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/slack/slack-original.svg">
      </div>

      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <img alt="Notion" class="w-12 h-12 group-hover:scale-110 transition-transform duration-300"
             src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/notion/notion-original.svg">
      </div>

      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <img alt="Google Workspace" class="w-12 h-12 group-hover:scale-110 transition-transform duration-300"
             src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/google/google-original.svg">
      </div>

      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <svg class="w-12 h-12 group-hover:scale-110 transition-transform duration-300" fill="#00A4EF"
             viewBox="0 0 24 24">
          <path d="M0 0h11.377v11.372H0zm12.623 0H24v11.372H12.623zM0 12.623h11.377V24H0zm12.623 0H24V24H12.623z"/>
        </svg>
        <span class="sr-only">Microsoft</span>
      </div>

      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <svg class="w-12 h-12 group-hover:scale-110 transition-transform duration-300" fill="#FF6900"
             viewBox="0 0 24 24">
          <path
            d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2.4c5.302 0 9.6 4.298 9.6 9.6s-4.298 9.6-9.6 9.6S2.4 17.302 2.4 12 6.698 2.4 12 2.4zm0 1.2c-4.64 0-8.4 3.76-8.4 8.4s3.76 8.4 8.4 8.4 8.4-3.76 8.4-8.4-3.76-8.4-8.4-8.4z"/>
        </svg>
        <span class="sr-only">ClickUp</span>
      </div>

      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <svg class="w-12 h-12 group-hover:scale-110 transition-transform duration-300" fill="#FF5A1F"
             viewBox="0 0 24 24">
          <path
            d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2zm-1.5 3v6l5.196 3L17 12.804l-4.5-2.598V5H10.5z"/>
        </svg>
        <span class="sr-only">Airtable</span>
      </div>

      <!-- Row 2 -->
      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <svg class="w-12 h-12 group-hover:scale-110 transition-transform duration-300" fill="#FF5722"
             viewBox="0 0 24 24">
          <path
            d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 4.8c3.984 0 7.2 3.216 7.2 7.2s-3.216 7.2-7.2 7.2S4.8 15.984 4.8 12 8.016 4.8 12 4.8z"/>
        </svg>
        <span class="sr-only">Make.com</span>
      </div>

      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <svg class="w-12 h-12 group-hover:scale-110 transition-transform duration-300" fill="#00D4AA"
             viewBox="0 0 24 24">
          <path
            d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 3c4.97 0 9 4.03 9 9s-4.03 9-9 9-9-4.03-9-9 4.03-9 9-9z"/>
        </svg>
        <span class="sr-only">Monday.com</span>
      </div>

      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <svg class="w-12 h-12 group-hover:scale-110 transition-transform duration-300" fill="#FF6154"
             viewBox="0 0 24 24">
          <path
            d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"/>
        </svg>
        <span class="sr-only">HubSpot</span>
      </div>

      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <svg class="w-12 h-12 group-hover:scale-110 transition-transform duration-300" fill="#10A37F"
             viewBox="0 0 24 24">
          <path
            d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 19.7a6.0462 6.0462 0 0 0 3.9977-2.9001 6.0651 6.0651 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142-.0852 4.783-2.7582a.7712.7712 0 0 0 .7806 0l5.8428 3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142.0852-4.7735 2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z"/>
        </svg>
        <span class="sr-only">ChatGPT</span>
      </div>

      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <svg class="w-12 h-12 group-hover:scale-110 transition-transform duration-300" fill="#D24939"
             viewBox="0 0 24 24">
          <path
            d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"/>
        </svg>
        <span class="sr-only">Zapier</span>
      </div>

      <div
        class="flex justify-center items-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group">
        <svg class="w-12 h-12 group-hover:scale-110 transition-transform duration-300" fill="#4285F4"
             viewBox="0 0 24 24">
          <path
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        <span class="sr-only">Google Workspace</span>
      </div>
    </div>

    <!-- Additional Text -->
    <div class="text-center mt-16">
      <p class="text-lg text-gray-500 max-w-2xl mx-auto">
        And many more tools in your existing tech stack. We adapt to your workflow, not the other way around.
      </p>
    </div>
  </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-32 bg-gradient-to-br from-[#fff0f9] via-[#f5f0ff] to-[#fef6fb] relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute top-0 left-0 w-96 h-96 bg-[#7125bb] rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-[#d734b1] rounded-full blur-3xl"></div>
  </div>

  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-24">
      <h2 class="text-5xl md:text-6xl font-bold mb-8 leading-tight">
        <span
          class="bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent">Why 8-Figure Companies</span>
        <span class="block text-[#7125bb]">Choose Chainmatic</span>
      </h2>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
        We don't just build tools - we architect intelligent systems that scale businesses from 6 to 8 figures
      </p>
      <div class="w-32 h-1 bg-gradient-to-r from-[#7125bb] to-[#d734b1] mx-auto mt-8 rounded-full"></div>
    </div>

    <!-- Benefits -->
    <div class="grid md:grid-cols-3 gap-16 relative">
      <!-- Benefit 1 -->
      <div class="text-center relative group">
        <div class="relative">
          <div
            class="w-40 h-40 bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl group-hover:scale-110 transition-all duration-500 border border-white/30">
            <ng-lottie [options]="successOptions" class="w-20 h-20"></ng-lottie>
          </div>
          <!-- Glow effect -->
          <div
            class="absolute inset-0 w-40 h-40 bg-[#7125bb]/20 rounded-3xl mx-auto blur-xl group-hover:blur-2xl transition-all duration-500"></div>
        </div>
        <h3 class="text-3xl font-bold text-[#7125bb] mb-6 group-hover:text-[#c55ba1] transition-colors duration-300">
          Proven Systems</h3>
        <p class="text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300">
          Battle-tested automation frameworks used by companies generating $10M+ annually. No experimental tech - only
          what works at scale.
        </p>
      </div>

      <!-- Benefit 2 -->
      <div class="text-center relative group">
        <div class="relative">
          <div
            class="w-40 h-40 bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl group-hover:scale-110 transition-all duration-500 border border-white/30">
            <ng-lottie [options]="growthOptions" class="w-20 h-20"></ng-lottie>
          </div>
          <div
            class="absolute inset-0 w-40 h-40 bg-[#c55ba1]/20 rounded-3xl mx-auto blur-xl group-hover:blur-2xl transition-all duration-500"></div>
        </div>
        <h3 class="text-3xl font-bold text-[#7125bb] mb-6 group-hover:text-[#d734b1] transition-colors duration-300">ROI
          Guaranteed</h3>
        <p class="text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300">
          Our automations pay for themselves within 90 days through reduced payroll costs and increased efficiency.
          Typical clients save $50K+ annually.
        </p>
      </div>

      <!-- Benefit 3 -->
      <div class="text-center relative group">
        <div class="relative">
          <div
            class="w-40 h-40 bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl group-hover:scale-110 transition-all duration-500 border border-white/30">
            <ng-lottie [options]="gearsOptions" class="w-20 h-20"></ng-lottie>
          </div>
          <div
            class="absolute inset-0 w-40 h-40 bg-[#d734b1]/20 rounded-3xl mx-auto blur-xl group-hover:blur-2xl transition-all duration-500"></div>
        </div>
        <h3 class="text-3xl font-bold text-[#7125bb] mb-6 group-hover:text-[#b46fc1] transition-colors duration-300">
          Elite Partnership</h3>
        <p class="text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300">
          We become your automation partner, not just a vendor. Ongoing optimization, strategic consulting, and growth
          planning included.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-32 bg-gradient-to-b from-[#f5f0ff] via-white to-[#fff0f9] relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0 opacity-5">
    <div
      class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-[#7125bb] to-[#d734b1] rounded-full blur-3xl"></div>
    <div
      class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-[#c55ba1] to-[#b46fc1] rounded-full blur-3xl"></div>
  </div>

  <div class="max-w-6xl mx-auto text-center px-6 relative z-10">
    <!-- Main CTA -->
    <div class="bg-white/80 backdrop-blur-sm border border-[#e5d6f0] rounded-3xl p-12 md:p-16 shadow-2xl">
      <h2
        class="text-5xl md:text-7xl font-bold bg-gradient-to-r from-[#7125bb] to-[#d734b1] bg-clip-text text-transparent mb-8 leading-tight">
        Ready to Scale to 8 Figures?
      </h2>
      <p class="text-2xl md:text-3xl text-gray-600 mb-12 leading-relaxed font-light">
        Stop drowning in operational chaos. Let AI handle the work while you focus on exponential growth.
      </p>

      <!-- Key Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-12 mb-16">
        <div class="text-center group">
          <div
            class="bg-gradient-to-br from-[#7125bb] to-[#c55ba1] rounded-2xl p-8 group-hover:scale-105 transition-all duration-500 shadow-xl">
            <div class="text-5xl font-bold text-white mb-3">$50K+</div>
            <div class="text-white/90 text-lg font-medium">Annual Savings</div>
          </div>
        </div>
        <div class="text-center group">
          <div
            class="bg-gradient-to-br from-[#c55ba1] to-[#d734b1] rounded-2xl p-8 group-hover:scale-105 transition-all duration-500 shadow-xl">
            <div class="text-5xl font-bold text-white mb-3">90 Days</div>
            <div class="text-white/90 text-lg font-medium">ROI Timeline</div>
          </div>
        </div>
        <div class="text-center group">
          <div
            class="bg-gradient-to-br from-[#d734b1] to-[#b46fc1] rounded-2xl p-8 group-hover:scale-105 transition-all duration-500 shadow-xl">
            <div class="text-5xl font-bold text-white mb-3">8-Figure</div>
            <div class="text-white/90 text-lg font-medium">Proven Results</div>
          </div>
        </div>
      </div>

      <!-- CTA Buttons -->
      <div class="flex flex-col sm:flex-row gap-8 justify-center items-center mb-12">
        <a
          class="group relative px-16 py-6 bg-gradient-to-r from-[#7125bb] to-[#c55ba1] rounded-2xl text-white font-bold text-2xl hover:from-[#c55ba1] hover:to-[#d734b1] transition-all duration-500 transform hover:scale-105 shadow-2xl overflow-hidden"
          href="mailto:<EMAIL>?subject=AI Automation Consultation&body=Hi, I'm interested in learning more about your AI automation solutions for my business.">
          <div
            class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
          <span class="relative z-10 flex items-center gap-4">
            <svg class="w-8 h-8 group-hover:rotate-12 transition-transform duration-300" fill="currentColor"
                 viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
            </svg>
            Book Free Consultation
          </span>
        </a>

        <a
          class="group px-16 py-6 border-3 border-[#7125bb] rounded-2xl text-[#7125bb] font-bold text-2xl hover:bg-[#7125bb] hover:text-white transition-all duration-500 transform hover:scale-105 bg-white shadow-lg"
          routerLink="/admin/dashboard">
          <span class="flex items-center gap-4">
            <svg class="w-8 h-8 group-hover:translate-x-2 transition-transform duration-300" fill="currentColor"
                 viewBox="0 0 20 20">
              <path clip-rule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    fill-rule="evenodd"></path>
            </svg>
            Client Portal
          </span>
        </a>
      </div>

      <!-- Trust Indicators -->
      <div class="bg-[#f5f0ff]/50 border border-[#e5d6f0] rounded-2xl p-6 inline-block">
        <p class="text-[#7125bb] text-lg font-medium">
          🔒 No commitment required • Free 30-minute strategy session • Instant automation audit
        </p>
      </div>
    </div>
  </div>
</section>
