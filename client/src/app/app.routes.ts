import { Routes } from '@angular/router';
import { ProposalResolver } from './resolvers';
import { authGuard } from './guards';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./pages/home/<USER>').then(
        (m) => m.HomeComponent,
      ),
  },
  {
    path: 'sign-in',
    loadComponent: () =>
      import('./pages/sign-in/sign-in.component').then(
        (m) => m.SignInComponent,
      ),
  },
  {
    path: 'proposals/:id',
    loadComponent: () =>
      import('./pages/proposal-details/proposal-details.component').then(
        (m) => m.ProposalDetailsComponent,
      ),
    resolve: {
      proposal: ProposalResolver,
    },
  },
  {
    path: 'admin',
    canActivate: [authGuard],
    children: [
      {
        path: 'dashboard',
        loadComponent: () =>
          import('./pages/dashboard/dashboard.component').then(
            (m) => m.DashboardComponent,
          ),
      },
      {
        path: 'proposal-editor',
        loadComponent: () =>
          import('./pages/proposal-editor/proposal-editor.component').then(
            (m) => m.ProposalEditorComponent,
          ),
      },
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
    ],
  },
  {
    path: 'not-found',
    loadComponent: () =>
      import('./pages/not-found/not-found.component').then(
        (m) => m.NotFoundComponent,
      ),
  },
  {
    path: 'successful-payment',
    loadComponent: () =>
      import('./pages/successful-payment/successful-payment.component').then(
        (m) => m.SuccessfulPaymentComponent,
      ),
  },
  { path: '**', redirectTo: 'not-found' },
];
